{"name": "ecommerce-backend", "version": "1.0.0", "description": "Backend API for ecommerce application", "main": "src/server.js", "scripts": {"dev": "nodemon src/server.js", "start": "node src/server.js", "build": "echo 'No build step required for Node.js'"}, "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-validator": "^7.0.1", "helmet": "^7.1.0", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["express", "mongodb", "ecommerce", "api"], "author": "", "license": "ISC"}