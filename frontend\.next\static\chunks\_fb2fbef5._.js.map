{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Ecommerce/ecommerce-app/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { createContext, useContext, useState, useEffect } from 'react';\r\nimport Cookies from 'js-cookie';\r\nimport { User } from '@/types';\r\n\r\ninterface AuthContextType {\r\n  user: User | null;\r\n  login: (email: string, password: string, role: 'admin' | 'user') => Promise<boolean>;\r\n  register: (userData: RegisterData) => Promise<boolean>;\r\n  logout: () => void;\r\n  isLoading: boolean;\r\n}\r\n\r\ninterface RegisterData {\r\n  name: string;\r\n  email: string;\r\n  password: string;\r\n  phone?: string;\r\n}\r\n\r\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\r\n\r\nexport const useAuth = () => {\r\n  const context = useContext(AuthContext);\r\n  if (context === undefined) {\r\n    throw new Error('useAuth must be used within an AuthProvider');\r\n  }\r\n  return context;\r\n};\r\n\r\nexport const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\r\n  const [user, setUser] = useState<User | null>(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n\r\n  // Check for existing session on mount\r\n  useEffect(() => {\r\n    const token = Cookies.get('auth-token');\r\n    const userData = Cookies.get('user-data');\r\n    \r\n    if (token && userData) {\r\n      try {\r\n        const parsedUser = JSON.parse(userData);\r\n        setUser(parsedUser);\r\n      } catch (error) {\r\n        console.error('Error parsing user data:', error);\r\n        Cookies.remove('auth-token');\r\n        Cookies.remove('user-data');\r\n      }\r\n    }\r\n    setIsLoading(false);\r\n  }, []);\r\n\r\n  const login = async (email: string, password: string, role: 'admin' | 'user'): Promise<boolean> => {\r\n    setIsLoading(true);\r\n    \r\n    try {\r\n      const response = await fetch('/api/auth/login', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({ email, password, role }),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (response.ok && data.success) {\r\n        const userData: User = {\r\n          id: data.user.id,\r\n          email: data.user.email,\r\n          name: data.user.name,\r\n          role: data.user.role,\r\n          phone: data.user.phone,\r\n          createdAt: new Date(data.user.createdAt),\r\n          updatedAt: new Date(data.user.updatedAt),\r\n        };\r\n\r\n        setUser(userData);\r\n        \r\n        // Store in cookies\r\n        Cookies.set('auth-token', data.token, { expires: 7 }); // 7 days\r\n        Cookies.set('user-data', JSON.stringify(userData), { expires: 7 });\r\n        \r\n        return true;\r\n      } else {\r\n        console.error('Login failed:', data.message);\r\n        return false;\r\n      }\r\n    } catch (error) {\r\n      console.error('Login error:', error);\r\n      return false;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const register = async (userData: RegisterData): Promise<boolean> => {\r\n    setIsLoading(true);\r\n    \r\n    try {\r\n      const response = await fetch('/api/auth/register', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify(userData),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (response.ok && data.success) {\r\n        // Auto-login after successful registration\r\n        return await login(userData.email, userData.password, 'user');\r\n      } else {\r\n        console.error('Registration failed:', data.message);\r\n        return false;\r\n      }\r\n    } catch (error) {\r\n      console.error('Registration error:', error);\r\n      return false;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const logout = () => {\r\n    setUser(null);\r\n    Cookies.remove('auth-token');\r\n    Cookies.remove('user-data');\r\n    window.location.href = '/';\r\n  };\r\n\r\n  const value = {\r\n    user,\r\n    login,\r\n    register,\r\n    logout,\r\n    isLoading,\r\n  };\r\n\r\n  return (\r\n    <AuthContext.Provider value={value}>\r\n      {children}\r\n    </AuthContext.Provider>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAqBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,UAAU;;IACrB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANa;AAQN,MAAM,eAAwD,CAAC,EAAE,QAAQ,EAAE;;IAChF,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,QAAQ,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;YAC1B,MAAM,WAAW,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;YAE7B,IAAI,SAAS,UAAU;gBACrB,IAAI;oBACF,MAAM,aAAa,KAAK,KAAK,CAAC;oBAC9B,QAAQ;gBACV,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,4BAA4B;oBAC1C,wJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;oBACf,wJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;gBACjB;YACF;YACA,aAAa;QACf;iCAAG,EAAE;IAEL,MAAM,QAAQ,OAAO,OAAe,UAAkB;QACpD,aAAa;QAEb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAO;oBAAU;gBAAK;YAC/C;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,IAAI,KAAK,OAAO,EAAE;gBAC/B,MAAM,WAAiB;oBACrB,IAAI,KAAK,IAAI,CAAC,EAAE;oBAChB,OAAO,KAAK,IAAI,CAAC,KAAK;oBACtB,MAAM,KAAK,IAAI,CAAC,IAAI;oBACpB,MAAM,KAAK,IAAI,CAAC,IAAI;oBACpB,OAAO,KAAK,IAAI,CAAC,KAAK;oBACtB,WAAW,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS;oBACvC,WAAW,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS;gBACzC;gBAEA,QAAQ;gBAER,mBAAmB;gBACnB,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,cAAc,KAAK,KAAK,EAAE;oBAAE,SAAS;gBAAE,IAAI,SAAS;gBAChE,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,aAAa,KAAK,SAAS,CAAC,WAAW;oBAAE,SAAS;gBAAE;gBAEhE,OAAO;YACT,OAAO;gBACL,QAAQ,KAAK,CAAC,iBAAiB,KAAK,OAAO;gBAC3C,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,aAAa;QAEb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,IAAI,KAAK,OAAO,EAAE;gBAC/B,2CAA2C;gBAC3C,OAAO,MAAM,MAAM,SAAS,KAAK,EAAE,SAAS,QAAQ,EAAE;YACxD,OAAO;gBACL,QAAQ,KAAK,CAAC,wBAAwB,KAAK,OAAO;gBAClD,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,SAAS;QACb,QAAQ;QACR,wJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,wJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;IAnHa;KAAA", "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Ecommerce/ecommerce-app/frontend/src/contexts/CartContext.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { createContext, useContext, useState, useEffect } from 'react';\r\nimport Cookies from 'js-cookie';\r\nimport { useAuth } from './AuthContext';\r\n\r\ninterface CartItem {\r\n  id: string;\r\n  productId: string;\r\n  product: any;\r\n  quantity: number;\r\n  total: number;\r\n}\r\n\r\ninterface CartContextType {\r\n  cartItems: CartItem[];\r\n  cartCount: number;\r\n  cartTotal: number;\r\n  addToCart: (productId: string, quantity?: number) => Promise<boolean>;\r\n  removeFromCart: (productId: string) => Promise<boolean>;\r\n  updateQuantity: (productId: string, quantity: number) => Promise<boolean>;\r\n  clearCart: () => void;\r\n  isLoading: boolean;\r\n  refreshCart: () => Promise<void>;\r\n}\r\n\r\nconst CartContext = createContext<CartContextType | undefined>(undefined);\r\n\r\nexport const useCart = () => {\r\n  const context = useContext(CartContext);\r\n  if (context === undefined) {\r\n    throw new Error('useCart must be used within a CartProvider');\r\n  }\r\n  return context;\r\n};\r\n\r\nexport const CartProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\r\n  const { user } = useAuth();\r\n  const [cartItems, setCartItems] = useState<CartItem[]>([]);\r\n  const [cartCount, setCartCount] = useState(0);\r\n  const [cartTotal, setCartTotal] = useState(0);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  // Helper function to make authenticated API calls\r\n  const makeAuthenticatedRequest = async (url: string, options: RequestInit = {}) => {\r\n    const token = Cookies.get('auth-token');\r\n    return fetch(url, {\r\n      ...options,\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${token}`,\r\n        ...options.headers,\r\n      },\r\n    });\r\n  };\r\n\r\n  // Fetch cart data from backend\r\n  const refreshCart = async () => {\r\n    if (!user) {\r\n      setCartItems([]);\r\n      setCartCount(0);\r\n      setCartTotal(0);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setIsLoading(true);\r\n      const response = await makeAuthenticatedRequest('/api/cart');\r\n      const data = await response.json();\r\n\r\n      if (data.success) {\r\n        setCartItems(data.cart.items);\r\n        setCartCount(data.cart.totalItems);\r\n        setCartTotal(data.cart.totalAmount);\r\n      } else {\r\n        console.error('Failed to fetch cart:', data.message);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching cart:', error);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Add item to cart\r\n  const addToCart = async (productId: string, quantity: number = 1): Promise<boolean> => {\r\n    if (!user) {\r\n      alert('Please login to add items to cart');\r\n      return false;\r\n    }\r\n\r\n    try {\r\n      setIsLoading(true);\r\n      const response = await makeAuthenticatedRequest('/api/cart', {\r\n        method: 'POST',\r\n        body: JSON.stringify({ productId, quantity }),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (data.success) {\r\n        await refreshCart();\r\n        return true;\r\n      } else {\r\n        alert(data.message || 'Failed to add item to cart');\r\n        return false;\r\n      }\r\n    } catch (error) {\r\n      console.error('Error adding to cart:', error);\r\n      alert('Failed to add item to cart');\r\n      return false;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Remove item from cart\r\n  const removeFromCart = async (productId: string): Promise<boolean> => {\r\n    if (!user) return false;\r\n\r\n    try {\r\n      setIsLoading(true);\r\n      const response = await makeAuthenticatedRequest(`/api/cart?productId=${productId}`, {\r\n        method: 'DELETE',\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (data.success) {\r\n        await refreshCart();\r\n        return true;\r\n      } else {\r\n        alert(data.message || 'Failed to remove item from cart');\r\n        return false;\r\n      }\r\n    } catch (error) {\r\n      console.error('Error removing from cart:', error);\r\n      alert('Failed to remove item from cart');\r\n      return false;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Update item quantity\r\n  const updateQuantity = async (productId: string, quantity: number): Promise<boolean> => {\r\n    if (!user) return false;\r\n\r\n    try {\r\n      setIsLoading(true);\r\n      const response = await makeAuthenticatedRequest('/api/cart', {\r\n        method: 'PUT',\r\n        body: JSON.stringify({ productId, quantity }),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (data.success) {\r\n        await refreshCart();\r\n        return true;\r\n      } else {\r\n        alert(data.message || 'Failed to update cart');\r\n        return false;\r\n      }\r\n    } catch (error) {\r\n      console.error('Error updating cart:', error);\r\n      alert('Failed to update cart');\r\n      return false;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Clear cart\r\n  const clearCart = () => {\r\n    setCartItems([]);\r\n    setCartCount(0);\r\n    setCartTotal(0);\r\n  };\r\n\r\n  // Load cart when user changes\r\n  useEffect(() => {\r\n    if (user) {\r\n      refreshCart();\r\n    } else {\r\n      clearCart();\r\n    }\r\n  }, [user]);\r\n\r\n  const value = {\r\n    cartItems,\r\n    cartCount,\r\n    cartTotal,\r\n    addToCart,\r\n    removeFromCart,\r\n    updateQuantity,\r\n    clearCart,\r\n    isLoading,\r\n    refreshCart,\r\n  };\r\n\r\n  return (\r\n    <CartContext.Provider value={value}>\r\n      {children}\r\n    </CartContext.Provider>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AA0BA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,UAAU;;IACrB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANa;AAQN,MAAM,eAAwD,CAAC,EAAE,QAAQ,EAAE;;IAChF,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,kDAAkD;IAClD,MAAM,2BAA2B,OAAO,KAAa,UAAuB,CAAC,CAAC;QAC5E,MAAM,QAAQ,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;QAC1B,OAAO,MAAM,KAAK;YAChB,GAAG,OAAO;YACV,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBAClC,GAAG,QAAQ,OAAO;YACpB;QACF;IACF;IAEA,+BAA+B;IAC/B,MAAM,cAAc;QAClB,IAAI,CAAC,MAAM;YACT,aAAa,EAAE;YACf,aAAa;YACb,aAAa;YACb;QACF;QAEA,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,yBAAyB;YAChD,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,aAAa,KAAK,IAAI,CAAC,KAAK;gBAC5B,aAAa,KAAK,IAAI,CAAC,UAAU;gBACjC,aAAa,KAAK,IAAI,CAAC,WAAW;YACpC,OAAO;gBACL,QAAQ,KAAK,CAAC,yBAAyB,KAAK,OAAO;YACrD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,aAAa;QACf;IACF;IAEA,mBAAmB;IACnB,MAAM,YAAY,OAAO,WAAmB,WAAmB,CAAC;QAC9D,IAAI,CAAC,MAAM;YACT,MAAM;YACN,OAAO;QACT;QAEA,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,yBAAyB,aAAa;gBAC3D,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAW;gBAAS;YAC7C;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM;gBACN,OAAO;YACT,OAAO;gBACL,MAAM,KAAK,OAAO,IAAI;gBACtB,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;YACN,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,wBAAwB;IACxB,MAAM,iBAAiB,OAAO;QAC5B,IAAI,CAAC,MAAM,OAAO;QAElB,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,yBAAyB,CAAC,oBAAoB,EAAE,WAAW,EAAE;gBAClF,QAAQ;YACV;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM;gBACN,OAAO;YACT,OAAO;gBACL,MAAM,KAAK,OAAO,IAAI;gBACtB,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;YACN,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,uBAAuB;IACvB,MAAM,iBAAiB,OAAO,WAAmB;QAC/C,IAAI,CAAC,MAAM,OAAO;QAElB,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,yBAAyB,aAAa;gBAC3D,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAW;gBAAS;YAC7C;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM;gBACN,OAAO;YACT,OAAO;gBACL,MAAM,KAAK,OAAO,IAAI;gBACtB,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;YACN,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,aAAa;IACb,MAAM,YAAY;QAChB,aAAa,EAAE;QACf,aAAa;QACb,aAAa;IACf;IAEA,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,MAAM;gBACR;YACF,OAAO;gBACL;YACF;QACF;iCAAG;QAAC;KAAK;IAET,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;IA1Ka;;QACM,kIAAA,CAAA,UAAO;;;KADb", "debugId": null}}, {"offset": {"line": 362, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Ecommerce/ecommerce-app/frontend/src/contexts/WishlistContext.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { createContext, useContext, useState, useEffect } from 'react';\r\nimport Cookies from 'js-cookie';\r\nimport { useAuth } from './AuthContext';\r\n\r\ninterface WishlistItem {\r\n  id: string;\r\n  productId: string;\r\n  product: any;\r\n  addedAt: Date;\r\n}\r\n\r\ninterface WishlistContextType {\r\n  wishlistItems: WishlistItem[];\r\n  wishlistCount: number;\r\n  addToWishlist: (productId: string) => Promise<boolean>;\r\n  removeFromWishlist: (productId: string) => Promise<boolean>;\r\n  isInWishlist: (productId: string) => boolean;\r\n  clearWishlist: () => void;\r\n  isLoading: boolean;\r\n  refreshWishlist: () => Promise<void>;\r\n}\r\n\r\nconst WishlistContext = createContext<WishlistContextType | undefined>(undefined);\r\n\r\nexport const useWishlist = () => {\r\n  const context = useContext(WishlistContext);\r\n  if (context === undefined) {\r\n    throw new Error('useWishlist must be used within a WishlistProvider');\r\n  }\r\n  return context;\r\n};\r\n\r\nexport const WishlistProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\r\n  const { user } = useAuth();\r\n  const [wishlistItems, setWishlistItems] = useState<WishlistItem[]>([]);\r\n  const [wishlistCount, setWishlistCount] = useState(0);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  // Helper function to make authenticated API calls\r\n  const makeAuthenticatedRequest = async (url: string, options: RequestInit = {}) => {\r\n    const token = Cookies.get('auth-token');\r\n    return fetch(url, {\r\n      ...options,\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${token}`,\r\n        ...options.headers,\r\n      },\r\n    });\r\n  };\r\n\r\n  // Fetch wishlist data from backend\r\n  const refreshWishlist = async () => {\r\n    if (!user) {\r\n      setWishlistItems([]);\r\n      setWishlistCount(0);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setIsLoading(true);\r\n      const response = await makeAuthenticatedRequest('/api/wishlist');\r\n      const data = await response.json();\r\n\r\n      if (data.success) {\r\n        setWishlistItems(data.wishlist.items);\r\n        setWishlistCount(data.wishlist.totalItems);\r\n      } else {\r\n        console.error('Failed to fetch wishlist:', data.message);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching wishlist:', error);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Add item to wishlist\r\n  const addToWishlist = async (productId: string): Promise<boolean> => {\r\n    if (!user) {\r\n      alert('Please login to add items to wishlist');\r\n      return false;\r\n    }\r\n\r\n    try {\r\n      setIsLoading(true);\r\n      const response = await makeAuthenticatedRequest('/api/wishlist', {\r\n        method: 'POST',\r\n        body: JSON.stringify({ productId }),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (data.success) {\r\n        await refreshWishlist();\r\n        return true;\r\n      } else {\r\n        if (data.message === 'Item already in wishlist') {\r\n          alert('Item is already in your wishlist');\r\n        } else {\r\n          alert(data.message || 'Failed to add item to wishlist');\r\n        }\r\n        return false;\r\n      }\r\n    } catch (error) {\r\n      console.error('Error adding to wishlist:', error);\r\n      alert('Failed to add item to wishlist');\r\n      return false;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Remove item from wishlist\r\n  const removeFromWishlist = async (productId: string): Promise<boolean> => {\r\n    if (!user) return false;\r\n\r\n    try {\r\n      setIsLoading(true);\r\n      const response = await makeAuthenticatedRequest(`/api/wishlist?productId=${productId}`, {\r\n        method: 'DELETE',\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (data.success) {\r\n        await refreshWishlist();\r\n        return true;\r\n      } else {\r\n        alert(data.message || 'Failed to remove item from wishlist');\r\n        return false;\r\n      }\r\n    } catch (error) {\r\n      console.error('Error removing from wishlist:', error);\r\n      alert('Failed to remove item from wishlist');\r\n      return false;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Check if item is in wishlist\r\n  const isInWishlist = (productId: string): boolean => {\r\n    return wishlistItems.some(item => item.productId === productId);\r\n  };\r\n\r\n  // Clear wishlist\r\n  const clearWishlist = () => {\r\n    setWishlistItems([]);\r\n    setWishlistCount(0);\r\n  };\r\n\r\n  // Load wishlist when user changes\r\n  useEffect(() => {\r\n    if (user) {\r\n      refreshWishlist();\r\n    } else {\r\n      clearWishlist();\r\n    }\r\n  }, [user]);\r\n\r\n  const value = {\r\n    wishlistItems,\r\n    wishlistCount,\r\n    addToWishlist,\r\n    removeFromWishlist,\r\n    isInWishlist,\r\n    clearWishlist,\r\n    isLoading,\r\n    refreshWishlist,\r\n  };\r\n\r\n  return (\r\n    <WishlistContext.Provider value={value}>\r\n      {children}\r\n    </WishlistContext.Provider>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AAwBA,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAmC;AAEhE,MAAM,cAAc;;IACzB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANa;AAQN,MAAM,mBAA4D,CAAC,EAAE,QAAQ,EAAE;;IACpF,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,kDAAkD;IAClD,MAAM,2BAA2B,OAAO,KAAa,UAAuB,CAAC,CAAC;QAC5E,MAAM,QAAQ,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;QAC1B,OAAO,MAAM,KAAK;YAChB,GAAG,OAAO;YACV,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBAClC,GAAG,QAAQ,OAAO;YACpB;QACF;IACF;IAEA,mCAAmC;IACnC,MAAM,kBAAkB;QACtB,IAAI,CAAC,MAAM;YACT,iBAAiB,EAAE;YACnB,iBAAiB;YACjB;QACF;QAEA,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,yBAAyB;YAChD,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,iBAAiB,KAAK,QAAQ,CAAC,KAAK;gBACpC,iBAAiB,KAAK,QAAQ,CAAC,UAAU;YAC3C,OAAO;gBACL,QAAQ,KAAK,CAAC,6BAA6B,KAAK,OAAO;YACzD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,aAAa;QACf;IACF;IAEA,uBAAuB;IACvB,MAAM,gBAAgB,OAAO;QAC3B,IAAI,CAAC,MAAM;YACT,MAAM;YACN,OAAO;QACT;QAEA,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,yBAAyB,iBAAiB;gBAC/D,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAU;YACnC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM;gBACN,OAAO;YACT,OAAO;gBACL,IAAI,KAAK,OAAO,KAAK,4BAA4B;oBAC/C,MAAM;gBACR,OAAO;oBACL,MAAM,KAAK,OAAO,IAAI;gBACxB;gBACA,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;YACN,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,4BAA4B;IAC5B,MAAM,qBAAqB,OAAO;QAChC,IAAI,CAAC,MAAM,OAAO;QAElB,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,yBAAyB,CAAC,wBAAwB,EAAE,WAAW,EAAE;gBACtF,QAAQ;YACV;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM;gBACN,OAAO;YACT,OAAO;gBACL,MAAM,KAAK,OAAO,IAAI;gBACtB,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;YACN,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,+BAA+B;IAC/B,MAAM,eAAe,CAAC;QACpB,OAAO,cAAc,IAAI,CAAC,CAAA,OAAQ,KAAK,SAAS,KAAK;IACvD;IAEA,iBAAiB;IACjB,MAAM,gBAAgB;QACpB,iBAAiB,EAAE;QACnB,iBAAiB;IACnB;IAEA,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,MAAM;gBACR;YACF,OAAO;gBACL;YACF;QACF;qCAAG;QAAC;KAAK;IAET,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,gBAAgB,QAAQ;QAAC,OAAO;kBAC9B;;;;;;AAGP;IAjJa;;QACM,kIAAA,CAAA,UAAO;;;KADb", "debugId": null}}, {"offset": {"line": 541, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Ecommerce/ecommerce-app/frontend/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,4BAA4B,SAAU,iBAAiB;YACrD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,KAAK,CAAC,2BAA2B,CAAC,IAAI,CACjE,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 749, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Ecommerce/ecommerce-app/frontend/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 762, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Ecommerce/ecommerce-app/frontend/node_modules/js-cookie/dist/js.cookie.mjs"], "sourcesContent": ["/*! js-cookie v3.0.5 | MIT */\n/* eslint-disable no-var */\nfunction assign (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n    for (var key in source) {\n      target[key] = source[key];\n    }\n  }\n  return target\n}\n/* eslint-enable no-var */\n\n/* eslint-disable no-var */\nvar defaultConverter = {\n  read: function (value) {\n    if (value[0] === '\"') {\n      value = value.slice(1, -1);\n    }\n    return value.replace(/(%[\\dA-F]{2})+/gi, decodeURIComponent)\n  },\n  write: function (value) {\n    return encodeURIComponent(value).replace(\n      /%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,\n      decodeURIComponent\n    )\n  }\n};\n/* eslint-enable no-var */\n\n/* eslint-disable no-var */\n\nfunction init (converter, defaultAttributes) {\n  function set (name, value, attributes) {\n    if (typeof document === 'undefined') {\n      return\n    }\n\n    attributes = assign({}, defaultAttributes, attributes);\n\n    if (typeof attributes.expires === 'number') {\n      attributes.expires = new Date(Date.now() + attributes.expires * 864e5);\n    }\n    if (attributes.expires) {\n      attributes.expires = attributes.expires.toUTCString();\n    }\n\n    name = encodeURIComponent(name)\n      .replace(/%(2[346B]|5E|60|7C)/g, decodeURIComponent)\n      .replace(/[()]/g, escape);\n\n    var stringifiedAttributes = '';\n    for (var attributeName in attributes) {\n      if (!attributes[attributeName]) {\n        continue\n      }\n\n      stringifiedAttributes += '; ' + attributeName;\n\n      if (attributes[attributeName] === true) {\n        continue\n      }\n\n      // Considers RFC 6265 section 5.2:\n      // ...\n      // 3.  If the remaining unparsed-attributes contains a %x3B (\";\")\n      //     character:\n      // Consume the characters of the unparsed-attributes up to,\n      // not including, the first %x3B (\";\") character.\n      // ...\n      stringifiedAttributes += '=' + attributes[attributeName].split(';')[0];\n    }\n\n    return (document.cookie =\n      name + '=' + converter.write(value, name) + stringifiedAttributes)\n  }\n\n  function get (name) {\n    if (typeof document === 'undefined' || (arguments.length && !name)) {\n      return\n    }\n\n    // To prevent the for loop in the first place assign an empty array\n    // in case there are no cookies at all.\n    var cookies = document.cookie ? document.cookie.split('; ') : [];\n    var jar = {};\n    for (var i = 0; i < cookies.length; i++) {\n      var parts = cookies[i].split('=');\n      var value = parts.slice(1).join('=');\n\n      try {\n        var found = decodeURIComponent(parts[0]);\n        jar[found] = converter.read(value, found);\n\n        if (name === found) {\n          break\n        }\n      } catch (e) {}\n    }\n\n    return name ? jar[name] : jar\n  }\n\n  return Object.create(\n    {\n      set,\n      get,\n      remove: function (name, attributes) {\n        set(\n          name,\n          '',\n          assign({}, attributes, {\n            expires: -1\n          })\n        );\n      },\n      withAttributes: function (attributes) {\n        return init(this.converter, assign({}, this.attributes, attributes))\n      },\n      withConverter: function (converter) {\n        return init(assign({}, this.converter, converter), this.attributes)\n      }\n    },\n    {\n      attributes: { value: Object.freeze(defaultAttributes) },\n      converter: { value: Object.freeze(converter) }\n    }\n  )\n}\n\nvar api = init(defaultConverter, { path: '/' });\n/* eslint-enable no-var */\n\nexport { api as default };\n"], "names": [], "mappings": "AAAA,2BAA2B,GAC3B,yBAAyB;;;AACzB,SAAS,OAAQ,MAAM;IACrB,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QACzC,IAAI,SAAS,SAAS,CAAC,EAAE;QACzB,IAAK,IAAI,OAAO,OAAQ;YACtB,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QAC3B;IACF;IACA,OAAO;AACT;AACA,wBAAwB,GAExB,yBAAyB,GACzB,IAAI,mBAAmB;IACrB,MAAM,SAAU,KAAK;QACnB,IAAI,KAAK,CAAC,EAAE,KAAK,KAAK;YACpB,QAAQ,MAAM,KAAK,CAAC,GAAG,CAAC;QAC1B;QACA,OAAO,MAAM,OAAO,CAAC,oBAAoB;IAC3C;IACA,OAAO,SAAU,KAAK;QACpB,OAAO,mBAAmB,OAAO,OAAO,CACtC,4CACA;IAEJ;AACF;AACA,wBAAwB,GAExB,yBAAyB,GAEzB,SAAS,KAAM,SAAS,EAAE,iBAAiB;IACzC,SAAS,IAAK,IAAI,EAAE,KAAK,EAAE,UAAU;QACnC,IAAI,OAAO,aAAa,aAAa;YACnC;QACF;QAEA,aAAa,OAAO,CAAC,GAAG,mBAAmB;QAE3C,IAAI,OAAO,WAAW,OAAO,KAAK,UAAU;YAC1C,WAAW,OAAO,GAAG,IAAI,KAAK,KAAK,GAAG,KAAK,WAAW,OAAO,GAAG;QAClE;QACA,IAAI,WAAW,OAAO,EAAE;YACtB,WAAW,OAAO,GAAG,WAAW,OAAO,CAAC,WAAW;QACrD;QAEA,OAAO,mBAAmB,MACvB,OAAO,CAAC,wBAAwB,oBAChC,OAAO,CAAC,SAAS;QAEpB,IAAI,wBAAwB;QAC5B,IAAK,IAAI,iBAAiB,WAAY;YACpC,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE;gBAC9B;YACF;YAEA,yBAAyB,OAAO;YAEhC,IAAI,UAAU,CAAC,cAAc,KAAK,MAAM;gBACtC;YACF;YAEA,kCAAkC;YAClC,MAAM;YACN,iEAAiE;YACjE,iBAAiB;YACjB,2DAA2D;YAC3D,iDAAiD;YACjD,MAAM;YACN,yBAAyB,MAAM,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QACxE;QAEA,OAAQ,SAAS,MAAM,GACrB,OAAO,MAAM,UAAU,KAAK,CAAC,OAAO,QAAQ;IAChD;IAEA,SAAS,IAAK,IAAI;QAChB,IAAI,OAAO,aAAa,eAAgB,UAAU,MAAM,IAAI,CAAC,MAAO;YAClE;QACF;QAEA,mEAAmE;QACnE,uCAAuC;QACvC,IAAI,UAAU,SAAS,MAAM,GAAG,SAAS,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE;QAChE,IAAI,MAAM,CAAC;QACX,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,IAAI,QAAQ,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC;YAC7B,IAAI,QAAQ,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC;YAEhC,IAAI;gBACF,IAAI,QAAQ,mBAAmB,KAAK,CAAC,EAAE;gBACvC,GAAG,CAAC,MAAM,GAAG,UAAU,IAAI,CAAC,OAAO;gBAEnC,IAAI,SAAS,OAAO;oBAClB;gBACF;YACF,EAAE,OAAO,GAAG,CAAC;QACf;QAEA,OAAO,OAAO,GAAG,CAAC,KAAK,GAAG;IAC5B;IAEA,OAAO,OAAO,MAAM,CAClB;QACE;QACA;QACA,QAAQ,SAAU,IAAI,EAAE,UAAU;YAChC,IACE,MACA,IACA,OAAO,CAAC,GAAG,YAAY;gBACrB,SAAS,CAAC;YACZ;QAEJ;QACA,gBAAgB,SAAU,UAAU;YAClC,OAAO,KAAK,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE;QAC1D;QACA,eAAe,SAAU,SAAS;YAChC,OAAO,KAAK,OAAO,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,YAAY,IAAI,CAAC,UAAU;QACpE;IACF,GACA;QACE,YAAY;YAAE,OAAO,OAAO,MAAM,CAAC;QAAmB;QACtD,WAAW;YAAE,OAAO,OAAO,MAAM,CAAC;QAAW;IAC/C;AAEJ;AAEA,IAAI,MAAM,KAAK,kBAAkB;IAAE,MAAM;AAAI", "ignoreList": [0], "debugId": null}}]}