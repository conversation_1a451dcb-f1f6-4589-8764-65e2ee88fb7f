{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Ecommerce/ecommerce-app/frontend/src/components/ui/Button.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\n\r\ninterface ButtonProps {\r\n  children: React.ReactNode;\r\n  variant?: 'primary' | 'secondary' | 'outline' | 'danger';\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  onClick?: () => void;\r\n  type?: 'button' | 'submit' | 'reset';\r\n  disabled?: boolean;\r\n}\r\n\r\nexport const Button: React.FC<ButtonProps> = ({\r\n  children,\r\n  variant = 'primary',\r\n  size = 'md',\r\n  className = '',\r\n  onClick,\r\n  type = 'button',\r\n  disabled = false,\r\n}) => {\r\n  const baseClasses = 'font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';\r\n\r\n  const variantClasses = {\r\n    primary: 'bg-orange-600 hover:bg-orange-700 text-white focus:ring-orange-500',\r\n    secondary: 'bg-gray-600 hover:bg-gray-700 text-white focus:ring-gray-500',\r\n    outline: 'border-2 border-orange-600 text-orange-600 hover:bg-orange-600 hover:text-white focus:ring-orange-500',\r\n    danger: 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500',\r\n  };\r\n\r\n  const sizeClasses = {\r\n    sm: 'px-3 py-1.5 text-sm',\r\n    md: 'px-4 py-2 text-base',\r\n    lg: 'px-6 py-3 text-lg',\r\n  };\r\n\r\n  const disabledClasses = disabled ? 'opacity-50 cursor-not-allowed' : '';\r\n\r\n  return (\r\n    <button\r\n      type={type}\r\n      onClick={onClick}\r\n      disabled={disabled}\r\n      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${disabledClasses} ${className}`}\r\n    >\r\n      {children}\r\n    </button>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;;AAcO,MAAM,SAAgC,CAAC,EAC5C,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,OAAO,EACP,OAAO,QAAQ,EACf,WAAW,KAAK,EACjB;IACC,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,SAAS;QACT,QAAQ;IACV;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB,WAAW,kCAAkC;IAErE,qBACE,6LAAC;QACC,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW,GAAG,YAAY,CAAC,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,gBAAgB,CAAC,EAAE,WAAW;kBAE1G;;;;;;AAGP;KApCa", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Ecommerce/ecommerce-app/frontend/src/data/products.ts"], "sourcesContent": ["import { Product, ProductCategory } from '@/types';\r\n\r\n// Sample Categories\r\nexport const categories: ProductCategory[] = [\r\n  {\r\n    id: '1',\r\n    name: 'Whole Spices',\r\n    slug: 'whole-spices',\r\n    description: 'Fresh whole spices for maximum flavor and aroma',\r\n    image: '🌶️'\r\n  },\r\n  {\r\n    id: '2',\r\n    name: 'Ground Spices',\r\n    slug: 'ground-spices',\r\n    description: 'Finely ground spices ready for cooking',\r\n    image: '🥄'\r\n  },\r\n  {\r\n    id: '3',\r\n    name: 'Spice Blends',\r\n    slug: 'spice-blends',\r\n    description: 'Expertly crafted blends for authentic flavors',\r\n    image: '🌿'\r\n  },\r\n  {\r\n    id: '4',\r\n    name: 'Herbs',\r\n    slug: 'herbs',\r\n    description: 'Fresh and dried herbs for cooking and seasoning',\r\n    image: '🍃'\r\n  },\r\n  {\r\n    id: '5',\r\n    name: 'Seeds',\r\n    slug: 'seeds',\r\n    description: 'Aromatic seeds for tempering and flavoring',\r\n    image: '🌰'\r\n  }\r\n];\r\n\r\n// Sample Products\r\nexport const products: Product[] = [\r\n  {\r\n    id: '1',\r\n    name: 'Organic Turmeric Powder',\r\n    description: 'Premium organic turmeric powder with high curcumin content. Perfect for curries, golden milk, and health supplements.',\r\n    price: 12.99,\r\n    originalPrice: 15.99,\r\n    category: categories[1], // Ground Spices\r\n    images: ['/images/turmeric.jpg'],\r\n    inStock: true,\r\n    stockQuantity: 50,\r\n    weight: '100g',\r\n    origin: 'India',\r\n    tags: ['organic', 'anti-inflammatory', 'golden milk'],\r\n    rating: 4.8,\r\n    reviewCount: 124,\r\n    createdAt: new Date('2024-01-15'),\r\n    updatedAt: new Date('2024-01-15')\r\n  },\r\n  {\r\n    id: '2',\r\n    name: 'Ceylon Cinnamon Sticks',\r\n    description: 'Authentic Ceylon cinnamon sticks with sweet, delicate flavor. Perfect for baking, tea, and desserts.',\r\n    price: 18.99,\r\n    category: categories[0], // Whole Spices\r\n    images: ['/images/cinnamon.jpg'],\r\n    inStock: true,\r\n    stockQuantity: 30,\r\n    weight: '50g',\r\n    origin: 'Sri Lanka',\r\n    tags: ['ceylon', 'sweet', 'baking'],\r\n    rating: 4.9,\r\n    reviewCount: 89,\r\n    createdAt: new Date('2024-01-10'),\r\n    updatedAt: new Date('2024-01-10')\r\n  },\r\n  {\r\n    id: '3',\r\n    name: 'Garam Masala Blend',\r\n    description: 'Traditional Indian spice blend with cardamom, cinnamon, cloves, and more. Essential for authentic Indian cuisine.',\r\n    price: 14.99,\r\n    category: categories[2], // Spice Blends\r\n    images: ['/images/garam-masala.jpg'],\r\n    inStock: true,\r\n    stockQuantity: 40,\r\n    weight: '75g',\r\n    origin: 'India',\r\n    tags: ['indian', 'blend', 'curry'],\r\n    rating: 4.7,\r\n    reviewCount: 156,\r\n    createdAt: new Date('2024-01-12'),\r\n    updatedAt: new Date('2024-01-12')\r\n  },\r\n  {\r\n    id: '4',\r\n    name: 'Black Peppercorns',\r\n    description: 'Premium whole black peppercorns with intense flavor and aroma. Freshly sourced from Malabar coast.',\r\n    price: 16.99,\r\n    category: categories[0], // Whole Spices\r\n    images: ['/images/black-pepper.jpg'],\r\n    inStock: true,\r\n    stockQuantity: 60,\r\n    weight: '100g',\r\n    origin: 'India',\r\n    tags: ['malabar', 'whole', 'premium'],\r\n    rating: 4.6,\r\n    reviewCount: 78,\r\n    createdAt: new Date('2024-01-08'),\r\n    updatedAt: new Date('2024-01-08')\r\n  },\r\n  {\r\n    id: '5',\r\n    name: 'Smoked Paprika',\r\n    description: 'Spanish smoked paprika with rich, smoky flavor. Perfect for paella, grilled meats, and vegetables.',\r\n    price: 13.99,\r\n    category: categories[1], // Ground Spices\r\n    images: ['/images/paprika.jpg'],\r\n    inStock: true,\r\n    stockQuantity: 35,\r\n    weight: '80g',\r\n    origin: 'Spain',\r\n    tags: ['smoked', 'spanish', 'paella'],\r\n    rating: 4.8,\r\n    reviewCount: 92,\r\n    createdAt: new Date('2024-01-14'),\r\n    updatedAt: new Date('2024-01-14')\r\n  },\r\n  {\r\n    id: '6',\r\n    name: 'Cardamom Pods',\r\n    description: 'Green cardamom pods with intense aroma and flavor. Essential for Indian sweets, tea, and rice dishes.',\r\n    price: 24.99,\r\n    originalPrice: 29.99,\r\n    category: categories[0], // Whole Spices\r\n    images: ['/images/cardamom.jpg'],\r\n    inStock: true,\r\n    stockQuantity: 25,\r\n    weight: '50g',\r\n    origin: 'Guatemala',\r\n    tags: ['green', 'aromatic', 'premium'],\r\n    rating: 4.9,\r\n    reviewCount: 67,\r\n    createdAt: new Date('2024-01-11'),\r\n    updatedAt: new Date('2024-01-11')\r\n  },\r\n  {\r\n    id: '7',\r\n    name: 'Cumin Seeds',\r\n    description: 'Whole cumin seeds with earthy, warm flavor. Perfect for tempering, grinding, and Middle Eastern cuisine.',\r\n    price: 9.99,\r\n    category: categories[4], // Seeds\r\n    images: ['/images/cumin.jpg'],\r\n    inStock: true,\r\n    stockQuantity: 80,\r\n    weight: '100g',\r\n    origin: 'India',\r\n    tags: ['earthy', 'tempering', 'middle-eastern'],\r\n    rating: 4.5,\r\n    reviewCount: 134,\r\n    createdAt: new Date('2024-01-09'),\r\n    updatedAt: new Date('2024-01-09')\r\n  },\r\n  {\r\n    id: '8',\r\n    name: 'Dried Oregano',\r\n    description: 'Mediterranean dried oregano with robust flavor. Perfect for pizza, pasta, and Mediterranean dishes.',\r\n    price: 8.99,\r\n    category: categories[3], // Herbs\r\n    images: ['/images/oregano.jpg'],\r\n    inStock: true,\r\n    stockQuantity: 45,\r\n    weight: '30g',\r\n    origin: 'Greece',\r\n    tags: ['mediterranean', 'pizza', 'pasta'],\r\n    rating: 4.4,\r\n    reviewCount: 98,\r\n    createdAt: new Date('2024-01-13'),\r\n    updatedAt: new Date('2024-01-13')\r\n  },\r\n  {\r\n    id: '9',\r\n    name: 'Star Anise',\r\n    description: 'Whole star anise with sweet licorice flavor. Essential for Asian cuisine, mulled wine, and baking.',\r\n    price: 19.99,\r\n    category: categories[0], // Whole Spices\r\n    images: ['/images/star-anise.jpg'],\r\n    inStock: false,\r\n    stockQuantity: 0,\r\n    weight: '40g',\r\n    origin: 'China',\r\n    tags: ['licorice', 'asian', 'baking'],\r\n    rating: 4.7,\r\n    reviewCount: 45,\r\n    createdAt: new Date('2024-01-07'),\r\n    updatedAt: new Date('2024-01-07')\r\n  },\r\n  {\r\n    id: '10',\r\n    name: 'Curry Powder',\r\n    description: 'Mild curry powder blend with turmeric, coriander, and cumin. Perfect for beginners to Indian cooking.',\r\n    price: 11.99,\r\n    category: categories[2], // Spice Blends\r\n    images: ['/images/curry-powder.jpg'],\r\n    inStock: true,\r\n    stockQuantity: 55,\r\n    weight: '100g',\r\n    origin: 'India',\r\n    tags: ['mild', 'beginner', 'indian'],\r\n    rating: 4.3,\r\n    reviewCount: 187,\r\n    createdAt: new Date('2024-01-06'),\r\n    updatedAt: new Date('2024-01-06')\r\n  }\r\n];\r\n"], "names": [], "mappings": ";;;;AAGO,MAAM,aAAgC;IAC3C;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;IACT;CACD;AAGM,MAAM,WAAsB;IACjC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,eAAe;QACf,UAAU,UAAU,CAAC,EAAE;QACvB,QAAQ;YAAC;SAAuB;QAChC,SAAS;QACT,eAAe;QACf,QAAQ;QACR,QAAQ;QACR,MAAM;YAAC;YAAW;YAAqB;SAAc;QACrD,QAAQ;QACR,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU,UAAU,CAAC,EAAE;QACvB,QAAQ;YAAC;SAAuB;QAChC,SAAS;QACT,eAAe;QACf,QAAQ;QACR,QAAQ;QACR,MAAM;YAAC;YAAU;YAAS;SAAS;QACnC,QAAQ;QACR,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU,UAAU,CAAC,EAAE;QACvB,QAAQ;YAAC;SAA2B;QACpC,SAAS;QACT,eAAe;QACf,QAAQ;QACR,QAAQ;QACR,MAAM;YAAC;YAAU;YAAS;SAAQ;QAClC,QAAQ;QACR,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU,UAAU,CAAC,EAAE;QACvB,QAAQ;YAAC;SAA2B;QACpC,SAAS;QACT,eAAe;QACf,QAAQ;QACR,QAAQ;QACR,MAAM;YAAC;YAAW;YAAS;SAAU;QACrC,QAAQ;QACR,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU,UAAU,CAAC,EAAE;QACvB,QAAQ;YAAC;SAAsB;QAC/B,SAAS;QACT,eAAe;QACf,QAAQ;QACR,QAAQ;QACR,MAAM;YAAC;YAAU;YAAW;SAAS;QACrC,QAAQ;QACR,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,eAAe;QACf,UAAU,UAAU,CAAC,EAAE;QACvB,QAAQ;YAAC;SAAuB;QAChC,SAAS;QACT,eAAe;QACf,QAAQ;QACR,QAAQ;QACR,MAAM;YAAC;YAAS;YAAY;SAAU;QACtC,QAAQ;QACR,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU,UAAU,CAAC,EAAE;QACvB,QAAQ;YAAC;SAAoB;QAC7B,SAAS;QACT,eAAe;QACf,QAAQ;QACR,QAAQ;QACR,MAAM;YAAC;YAAU;YAAa;SAAiB;QAC/C,QAAQ;QACR,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU,UAAU,CAAC,EAAE;QACvB,QAAQ;YAAC;SAAsB;QAC/B,SAAS;QACT,eAAe;QACf,QAAQ;QACR,QAAQ;QACR,MAAM;YAAC;YAAiB;YAAS;SAAQ;QACzC,QAAQ;QACR,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU,UAAU,CAAC,EAAE;QACvB,QAAQ;YAAC;SAAyB;QAClC,SAAS;QACT,eAAe;QACf,QAAQ;QACR,QAAQ;QACR,MAAM;YAAC;YAAY;YAAS;SAAS;QACrC,QAAQ;QACR,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU,UAAU,CAAC,EAAE;QACvB,QAAQ;YAAC;SAA2B;QACpC,SAAS;QACT,eAAe;QACf,QAAQ;QACR,QAAQ;QACR,MAAM;YAAC;YAAQ;YAAY;SAAS;QACpC,QAAQ;QACR,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD", "debugId": null}}, {"offset": {"line": 335, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Ecommerce/ecommerce-app/frontend/src/components/ui/SearchSuggestions.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useRef } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { products } from '@/data/products';\r\n\r\ninterface SearchSuggestionsProps {\r\n  searchQuery: string;\r\n  onSelect: (query: string) => void;\r\n  isVisible: boolean;\r\n  onClose: () => void;\r\n}\r\n\r\nexport const SearchSuggestions: React.FC<SearchSuggestionsProps> = ({\r\n  searchQuery,\r\n  onSelect,\r\n  isVisible,\r\n  onClose,\r\n}) => {\r\n  const router = useRouter();\r\n  const [suggestions, setSuggestions] = useState<string[]>([]);\r\n  const suggestionsRef = useRef<HTMLDivElement>(null);\r\n\r\n  useEffect(() => {\r\n    if (searchQuery.length > 1) {\r\n      const query = searchQuery.toLowerCase();\r\n      const productSuggestions = new Set<string>();\r\n\r\n      products.forEach(product => {\r\n        // Add product names that match\r\n        if (product.name.toLowerCase().includes(query)) {\r\n          productSuggestions.add(product.name);\r\n        }\r\n        \r\n        // Add category names that match\r\n        if (product.category.name.toLowerCase().includes(query)) {\r\n          productSuggestions.add(product.category.name);\r\n        }\r\n        \r\n        // Add tags that match\r\n        product.tags.forEach(tag => {\r\n          if (tag.toLowerCase().includes(query)) {\r\n            productSuggestions.add(tag);\r\n          }\r\n        });\r\n        \r\n        // Add origins that match\r\n        if (product.origin.toLowerCase().includes(query)) {\r\n          productSuggestions.add(product.origin);\r\n        }\r\n      });\r\n\r\n      setSuggestions(Array.from(productSuggestions).slice(0, 6));\r\n    } else {\r\n      setSuggestions([]);\r\n    }\r\n  }, [searchQuery]);\r\n\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      if (suggestionsRef.current && !suggestionsRef.current.contains(event.target as Node)) {\r\n        onClose();\r\n      }\r\n    };\r\n\r\n    if (isVisible) {\r\n      document.addEventListener('mousedown', handleClickOutside);\r\n    }\r\n\r\n    return () => {\r\n      document.removeEventListener('mousedown', handleClickOutside);\r\n    };\r\n  }, [isVisible, onClose]);\r\n\r\n  if (!isVisible || suggestions.length === 0) {\r\n    return null;\r\n  }\r\n\r\n  const handleSuggestionClick = (suggestion: string) => {\r\n    onSelect(suggestion);\r\n    router.push(`/products?search=${encodeURIComponent(suggestion)}`);\r\n  };\r\n\r\n  return (\r\n    <div\r\n      ref={suggestionsRef}\r\n      className=\"absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg z-50 mt-1\"\r\n    >\r\n      <div className=\"py-2\">\r\n        {suggestions.map((suggestion, index) => (\r\n          <button\r\n            key={index}\r\n            onClick={() => handleSuggestionClick(suggestion)}\r\n            className=\"w-full px-4 py-2 text-left hover:bg-gray-100 transition-colors flex items-center\"\r\n          >\r\n            <svg className=\"h-4 w-4 text-gray-400 mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\r\n            </svg>\r\n            <span className=\"text-gray-900\">{suggestion}</span>\r\n          </button>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAaO,MAAM,oBAAsD,CAAC,EAClE,WAAW,EACX,QAAQ,EACR,SAAS,EACT,OAAO,EACR;;IACC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC3D,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,YAAY,MAAM,GAAG,GAAG;gBAC1B,MAAM,QAAQ,YAAY,WAAW;gBACrC,MAAM,qBAAqB,IAAI;gBAE/B,0HAAA,CAAA,WAAQ,CAAC,OAAO;mDAAC,CAAA;wBACf,+BAA+B;wBAC/B,IAAI,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ;4BAC9C,mBAAmB,GAAG,CAAC,QAAQ,IAAI;wBACrC;wBAEA,gCAAgC;wBAChC,IAAI,QAAQ,QAAQ,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ;4BACvD,mBAAmB,GAAG,CAAC,QAAQ,QAAQ,CAAC,IAAI;wBAC9C;wBAEA,sBAAsB;wBACtB,QAAQ,IAAI,CAAC,OAAO;2DAAC,CAAA;gCACnB,IAAI,IAAI,WAAW,GAAG,QAAQ,CAAC,QAAQ;oCACrC,mBAAmB,GAAG,CAAC;gCACzB;4BACF;;wBAEA,yBAAyB;wBACzB,IAAI,QAAQ,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ;4BAChD,mBAAmB,GAAG,CAAC,QAAQ,MAAM;wBACvC;oBACF;;gBAEA,eAAe,MAAM,IAAI,CAAC,oBAAoB,KAAK,CAAC,GAAG;YACzD,OAAO;gBACL,eAAe,EAAE;YACnB;QACF;sCAAG;QAAC;KAAY;IAEhB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM;kEAAqB,CAAC;oBAC1B,IAAI,eAAe,OAAO,IAAI,CAAC,eAAe,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBACpF;oBACF;gBACF;;YAEA,IAAI,WAAW;gBACb,SAAS,gBAAgB,CAAC,aAAa;YACzC;YAEA;+CAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;sCAAG;QAAC;QAAW;KAAQ;IAEvB,IAAI,CAAC,aAAa,YAAY,MAAM,KAAK,GAAG;QAC1C,OAAO;IACT;IAEA,MAAM,wBAAwB,CAAC;QAC7B,SAAS;QACT,OAAO,IAAI,CAAC,CAAC,iBAAiB,EAAE,mBAAmB,aAAa;IAClE;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;kBAEV,cAAA,6LAAC;YAAI,WAAU;sBACZ,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC5B,6LAAC;oBAEC,SAAS,IAAM,sBAAsB;oBACrC,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;4BAA6B,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACpF,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;sCAEvE,6LAAC;4BAAK,WAAU;sCAAiB;;;;;;;mBAP5B;;;;;;;;;;;;;;;AAajB;GA3Fa;;QAMI,qIAAA,CAAA,YAAS;;;KANb", "debugId": null}}, {"offset": {"line": 490, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Ecommerce/ecommerce-app/frontend/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport Link from 'next/link';\r\nimport { Button } from '@/components/ui/Button';\r\nimport { SearchSuggestions } from '@/components/ui/SearchSuggestions';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useCart } from '@/contexts/CartContext';\r\nimport { useWishlist } from '@/contexts/WishlistContext';\r\n\r\nexport const Header = () => {\r\n  const { user, logout } = useAuth();\r\n  const { cartCount } = useCart();\r\n  const { wishlistCount } = useWishlist();\r\n  const router = useRouter();\r\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n  const [isSearchOpen, setIsSearchOpen] = useState(false);\r\n  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n  const [showSuggestions, setShowSuggestions] = useState(false);\r\n\r\n  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);\r\n  const toggleSearch = () => setIsSearchOpen(!isSearchOpen);\r\n\r\n  const handleSearch = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (searchQuery.trim()) {\r\n      // Navigate to products page with search query\r\n      router.push(`/products?search=${encodeURIComponent(searchQuery.trim())}`);\r\n      setSearchQuery(''); // Clear search after navigation\r\n      setIsSearchOpen(false); // Close mobile search\r\n    }\r\n  };\r\n\r\n  return (\r\n    <header className=\"bg-white shadow-sm border-b sticky top-0 z-50\">\r\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n        <div className=\"flex justify-between items-center h-16\">\r\n          {/* Logo */}\r\n          <div className=\"flex items-center\">\r\n            <Link href=\"/\" className=\"flex items-center\">\r\n              <h1 className=\"text-2xl font-bold text-orange-600\">🌶️ SpiceHub</h1>\r\n            </Link>\r\n          </div>\r\n\r\n          {/* Desktop Navigation */}\r\n          <nav className=\"hidden md:flex space-x-8\">\r\n            <Link href=\"/\" className=\"text-gray-700 hover:text-orange-600 transition-colors\">\r\n              Home\r\n            </Link>\r\n            <Link href=\"/products\" className=\"text-gray-700 hover:text-orange-600 transition-colors\">\r\n              Products\r\n            </Link>\r\n            <Link href=\"/categories\" className=\"text-gray-700 hover:text-orange-600 transition-colors\">\r\n              Categories\r\n            </Link>\r\n            <Link href=\"/about\" className=\"text-gray-700 hover:text-orange-600 transition-colors\">\r\n              About\r\n            </Link>\r\n            <Link href=\"/contact\" className=\"text-gray-700 hover:text-orange-600 transition-colors\">\r\n              Contact\r\n            </Link>\r\n          </nav>\r\n\r\n          {/* Search Bar (Desktop) */}\r\n          <div className=\"hidden lg:flex flex-1 max-w-lg mx-8\">\r\n            <form onSubmit={handleSearch} className=\"w-full\">\r\n              <div className=\"relative\">\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"Search for spices...\"\r\n                  value={searchQuery}\r\n                  onChange={(e) => {\r\n                    setSearchQuery(e.target.value);\r\n                    setShowSuggestions(e.target.value.length > 1);\r\n                  }}\r\n                  onFocus={() => setShowSuggestions(searchQuery.length > 1)}\r\n                  className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\r\n                />\r\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                  <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\r\n                  </svg>\r\n                </div>\r\n                <SearchSuggestions\r\n                  searchQuery={searchQuery}\r\n                  onSelect={(suggestion) => {\r\n                    setSearchQuery(suggestion);\r\n                    setShowSuggestions(false);\r\n                  }}\r\n                  isVisible={showSuggestions}\r\n                  onClose={() => setShowSuggestions(false)}\r\n                />\r\n              </div>\r\n            </form>\r\n          </div>\r\n\r\n          {/* Right Side Icons & Buttons */}\r\n          <div className=\"flex items-center space-x-4\">\r\n            {/* Search Icon (Mobile) */}\r\n            <button\r\n              onClick={toggleSearch}\r\n              className=\"lg:hidden p-2 text-gray-600 hover:text-orange-600\"\r\n            >\r\n              <svg className=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\r\n              </svg>\r\n            </button>\r\n\r\n            {/* Cart Icon */}\r\n            <Link href=\"/cart\" className=\"relative p-2 text-gray-600 hover:text-orange-600 transition-colors\">\r\n              <svg className=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01\" />\r\n              </svg>\r\n              {/* Cart Badge */}\r\n              {cartCount > 0 && (\r\n                <span className=\"absolute -top-1 -right-1 bg-orange-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse\">\r\n                  {cartCount}\r\n                </span>\r\n              )}\r\n            </Link>\r\n\r\n            {/* Wishlist Icon */}\r\n            <Link href=\"/wishlist\" className=\"relative p-2 text-gray-600 hover:text-orange-600\">\r\n              <svg className=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\r\n              </svg>\r\n              {wishlistCount > 0 && (\r\n                <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\">\r\n                  {wishlistCount}\r\n                </span>\r\n              )}\r\n            </Link>\r\n\r\n            {/* Auth Section */}\r\n            {user ? (\r\n              <div className=\"relative\">\r\n                <button\r\n                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}\r\n                  className=\"flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-colors\"\r\n                >\r\n                  <div className=\"h-8 w-8 bg-orange-600 rounded-full flex items-center justify-center\">\r\n                    <span className=\"text-white text-sm font-semibold\">\r\n                      {user.name.charAt(0).toUpperCase()}\r\n                    </span>\r\n                  </div>\r\n                  <span className=\"hidden md:block text-gray-700 font-medium\">{user.name}</span>\r\n                  <svg className=\"h-4 w-4 text-gray-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\r\n                  </svg>\r\n                </button>\r\n\r\n                {/* User Dropdown Menu */}\r\n                {isUserMenuOpen && (\r\n                  <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50\">\r\n                    <div className=\"px-4 py-2 border-b border-gray-200\">\r\n                      <p className=\"text-sm font-medium text-gray-900\">{user.name}</p>\r\n                      <p className=\"text-sm text-gray-500\">{user.email}</p>\r\n                      <span className=\"inline-block mt-1 px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded-full\">\r\n                        {user.role === 'admin' ? 'Admin' : 'Customer'}\r\n                      </span>\r\n                    </div>\r\n                    <Link href=\"/profile\" className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">\r\n                      My Profile\r\n                    </Link>\r\n                    <Link href=\"/orders\" className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">\r\n                      My Orders\r\n                    </Link>\r\n                    <Link href=\"/wishlist\" className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">\r\n                      Wishlist\r\n                    </Link>\r\n                    {user.role === 'admin' && (\r\n                      <Link href=\"/admin/dashboard\" className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">\r\n                        Admin Dashboard\r\n                      </Link>\r\n                    )}\r\n                    <div className=\"border-t border-gray-200 mt-2 pt-2\">\r\n                      <button\r\n                        onClick={logout}\r\n                        className=\"block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100\"\r\n                      >\r\n                        Sign Out\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            ) : (\r\n              <div className=\"hidden sm:flex items-center space-x-2\">\r\n                <Link href=\"/auth/login\">\r\n                  <Button variant=\"outline\" size=\"sm\">Login</Button>\r\n                </Link>\r\n                <Link href=\"/auth/register\">\r\n                  <Button size=\"sm\">Sign Up</Button>\r\n                </Link>\r\n              </div>\r\n            )}\r\n\r\n            {/* Mobile Menu Button */}\r\n            <button\r\n              onClick={toggleMenu}\r\n              className=\"md:hidden p-2 text-gray-600 hover:text-orange-600\"\r\n            >\r\n              <svg className=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                {isMenuOpen ? (\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                ) : (\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\r\n                )}\r\n              </svg>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Mobile Search Bar */}\r\n        {isSearchOpen && (\r\n          <div className=\"lg:hidden py-4 border-t\">\r\n            <form onSubmit={handleSearch}>\r\n              <div className=\"relative\">\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"Search for spices...\"\r\n                  value={searchQuery}\r\n                  onChange={(e) => setSearchQuery(e.target.value)}\r\n                  className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\r\n                />\r\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                  <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n            </form>\r\n          </div>\r\n        )}\r\n\r\n        {/* Mobile Menu */}\r\n        {isMenuOpen && (\r\n          <div className=\"md:hidden py-4 border-t\">\r\n            <nav className=\"flex flex-col space-y-4\">\r\n              <Link href=\"/\" className=\"text-gray-700 hover:text-orange-600 transition-colors\">\r\n                Home\r\n              </Link>\r\n              <Link href=\"/products\" className=\"text-gray-700 hover:text-orange-600 transition-colors\">\r\n                Products\r\n              </Link>\r\n              <Link href=\"/categories\" className=\"text-gray-700 hover:text-orange-600 transition-colors\">\r\n                Categories\r\n              </Link>\r\n              <Link href=\"/about\" className=\"text-gray-700 hover:text-orange-600 transition-colors\">\r\n                About\r\n              </Link>\r\n              <Link href=\"/contact\" className=\"text-gray-700 hover:text-orange-600 transition-colors\">\r\n                Contact\r\n              </Link>\r\n              <div className=\"flex flex-col space-y-2 pt-4 border-t\">\r\n                {user ? (\r\n                  <>\r\n                    <div className=\"px-4 py-2 bg-gray-50 rounded-lg\">\r\n                      <p className=\"text-sm font-medium text-gray-900\">{user.name}</p>\r\n                      <p className=\"text-xs text-gray-500\">{user.email}</p>\r\n                      <span className=\"inline-block mt-1 px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded-full\">\r\n                        {user.role === 'admin' ? 'Admin' : 'Customer'}\r\n                      </span>\r\n                    </div>\r\n                    <Link href=\"/profile\" className=\"text-gray-700 hover:text-orange-600 transition-colors py-2\">\r\n                      My Profile\r\n                    </Link>\r\n                    <Link href=\"/orders\" className=\"text-gray-700 hover:text-orange-600 transition-colors py-2\">\r\n                      My Orders\r\n                    </Link>\r\n                    {user.role === 'admin' && (\r\n                      <Link href=\"/admin/dashboard\" className=\"text-gray-700 hover:text-orange-600 transition-colors py-2\">\r\n                        Admin Dashboard\r\n                      </Link>\r\n                    )}\r\n                    <button\r\n                      onClick={logout}\r\n                      className=\"text-left text-red-600 hover:text-red-700 transition-colors py-2\"\r\n                    >\r\n                      Sign Out\r\n                    </button>\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <Link href=\"/auth/login\">\r\n                      <Button variant=\"outline\" size=\"sm\" className=\"w-full\">Login</Button>\r\n                    </Link>\r\n                    <Link href=\"/auth/register\">\r\n                      <Button size=\"sm\" className=\"w-full\">Sign Up</Button>\r\n                    </Link>\r\n                  </>\r\n                )}\r\n              </div>\r\n            </nav>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </header>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAWO,MAAM,SAAS;;IACpB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC5B,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IACpC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,aAAa,IAAM,cAAc,CAAC;IACxC,MAAM,eAAe,IAAM,gBAAgB,CAAC;IAE5C,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,YAAY,IAAI,IAAI;YACtB,8CAA8C;YAC9C,OAAO,IAAI,CAAC,CAAC,iBAAiB,EAAE,mBAAmB,YAAY,IAAI,KAAK;YACxE,eAAe,KAAK,gCAAgC;YACpD,gBAAgB,QAAQ,sBAAsB;QAChD;IACF;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CACvB,cAAA,6LAAC;oCAAG,WAAU;8CAAqC;;;;;;;;;;;;;;;;sCAKvD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAwD;;;;;;8CAGjF,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;8CAAwD;;;;;;8CAGzF,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAc,WAAU;8CAAwD;;;;;;8CAG3F,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAwD;;;;;;8CAGtF,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAwD;;;;;;;;;;;;sCAM1F,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,UAAU;gCAAc,WAAU;0CACtC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC;gDACT,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC7B,mBAAmB,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG;4CAC7C;4CACA,SAAS,IAAM,mBAAmB,YAAY,MAAM,GAAG;4CACvD,WAAU;;;;;;sDAEZ,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC/E,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC,gJAAA,CAAA,oBAAiB;4CAChB,aAAa;4CACb,UAAU,CAAC;gDACT,eAAe;gDACf,mBAAmB;4CACrB;4CACA,WAAW;4CACX,SAAS,IAAM,mBAAmB;;;;;;;;;;;;;;;;;;;;;;sCAO1C,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAKzE,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;;sDAC3B,6LAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCAGtE,YAAY,mBACX,6LAAC;4CAAK,WAAU;sDACb;;;;;;;;;;;;8CAMP,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;;sDAC/B,6LAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCAEtE,gBAAgB,mBACf,6LAAC;4CAAK,WAAU;sDACb;;;;;;;;;;;;gCAMN,qBACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,kBAAkB,CAAC;4CAClC,WAAU;;8DAEV,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEACb,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;8DAGpC,6LAAC;oDAAK,WAAU;8DAA6C,KAAK,IAAI;;;;;;8DACtE,6LAAC;oDAAI,WAAU;oDAAwB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC/E,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;wCAKxE,gCACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAqC,KAAK,IAAI;;;;;;sEAC3D,6LAAC;4DAAE,WAAU;sEAAyB,KAAK,KAAK;;;;;;sEAChD,6LAAC;4DAAK,WAAU;sEACb,KAAK,IAAI,KAAK,UAAU,UAAU;;;;;;;;;;;;8DAGvC,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;8DAA0D;;;;;;8DAG1F,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAU,WAAU;8DAA0D;;;;;;8DAGzF,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAY,WAAU;8DAA0D;;;;;;gDAG1F,KAAK,IAAI,KAAK,yBACb,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAmB,WAAU;8DAA0D;;;;;;8DAIpG,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,SAAS;wDACT,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;yDAQT,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;0DAAK;;;;;;;;;;;sDAEtC,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,MAAK;0DAAK;;;;;;;;;;;;;;;;;8CAMxB,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAChE,2BACC,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;iEAErE,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAQ9E,8BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAK,UAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,WAAU;;;;;;8CAEZ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;wCAAwB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC/E,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAShF,4BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAwD;;;;;;0CAGjF,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAY,WAAU;0CAAwD;;;;;;0CAGzF,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAc,WAAU;0CAAwD;;;;;;0CAG3F,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAS,WAAU;0CAAwD;;;;;;0CAGtF,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAW,WAAU;0CAAwD;;;;;;0CAGxF,6LAAC;gCAAI,WAAU;0CACZ,qBACC;;sDACE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAqC,KAAK,IAAI;;;;;;8DAC3D,6LAAC;oDAAE,WAAU;8DAAyB,KAAK,KAAK;;;;;;8DAChD,6LAAC;oDAAK,WAAU;8DACb,KAAK,IAAI,KAAK,UAAU,UAAU;;;;;;;;;;;;sDAGvC,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAA6D;;;;;;sDAG7F,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAU,WAAU;sDAA6D;;;;;;wCAG3F,KAAK,IAAI,KAAK,yBACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAmB,WAAU;sDAA6D;;;;;;sDAIvG,6LAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;;iEAKH;;sDACE,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,WAAU;0DAAS;;;;;;;;;;;sDAEzD,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,WAAU;0DAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW3D;GAlSa;;QACc,kIAAA,CAAA,UAAO;QACV,kIAAA,CAAA,UAAO;QACH,sIAAA,CAAA,cAAW;QACtB,qIAAA,CAAA,YAAS;;;KAJb", "debugId": null}}, {"offset": {"line": 1320, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Ecommerce/ecommerce-app/frontend/src/components/layout/Footer.tsx"], "sourcesContent": ["import Link from 'next/link';\r\n\r\nexport const Footer = () => {\r\n  return (\r\n    <footer className=\"bg-gray-900 text-white\">\r\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\r\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\r\n          {/* Brand Section */}\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold mb-4\">🌶️ SpiceHub</h3>\r\n            <p className=\"text-gray-400 mb-4\">\r\n              Your trusted source for premium spices and herbs. Fresh, authentic, and sourced directly from farms.\r\n            </p>\r\n            <div className=\"flex space-x-4\">\r\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\r\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path d=\"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z\"/>\r\n                </svg>\r\n              </a>\r\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\r\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path d=\"M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z\"/>\r\n                </svg>\r\n              </a>\r\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\r\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path d=\"M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z\"/>\r\n                </svg>\r\n              </a>\r\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\r\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path d=\"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z\"/>\r\n                </svg>\r\n              </a>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Quick Links */}\r\n          <div>\r\n            <h4 className=\"text-lg font-semibold mb-4\">Quick Links</h4>\r\n            <ul className=\"space-y-2 text-gray-400\">\r\n              <li><Link href=\"/products\" className=\"hover:text-white transition-colors\">All Products</Link></li>\r\n              <li><Link href=\"/categories\" className=\"hover:text-white transition-colors\">Categories</Link></li>\r\n              <li><Link href=\"/deals\" className=\"hover:text-white transition-colors\">Special Deals</Link></li>\r\n              <li><Link href=\"/new-arrivals\" className=\"hover:text-white transition-colors\">New Arrivals</Link></li>\r\n              <li><Link href=\"/bestsellers\" className=\"hover:text-white transition-colors\">Best Sellers</Link></li>\r\n            </ul>\r\n          </div>\r\n\r\n          {/* Customer Service */}\r\n          <div>\r\n            <h4 className=\"text-lg font-semibold mb-4\">Customer Service</h4>\r\n            <ul className=\"space-y-2 text-gray-400\">\r\n              <li><Link href=\"/help\" className=\"hover:text-white transition-colors\">Help Center</Link></li>\r\n              <li><Link href=\"/shipping\" className=\"hover:text-white transition-colors\">Shipping Info</Link></li>\r\n              <li><Link href=\"/returns\" className=\"hover:text-white transition-colors\">Returns & Exchanges</Link></li>\r\n              <li><Link href=\"/faq\" className=\"hover:text-white transition-colors\">FAQ</Link></li>\r\n              <li><Link href=\"/contact\" className=\"hover:text-white transition-colors\">Contact Us</Link></li>\r\n            </ul>\r\n          </div>\r\n\r\n          {/* Contact Info */}\r\n          <div>\r\n            <h4 className=\"text-lg font-semibold mb-4\">Get in Touch</h4>\r\n            <div className=\"space-y-3 text-gray-400\">\r\n              <div className=\"flex items-center\">\r\n                <svg className=\"h-5 w-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\r\n                </svg>\r\n                <span><EMAIL></span>\r\n              </div>\r\n              <div className=\"flex items-center\">\r\n                <svg className=\"h-5 w-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\r\n                </svg>\r\n                <span>(555) 123-4567</span>\r\n              </div>\r\n              <div className=\"flex items-start\">\r\n                <svg className=\"h-5 w-5 mr-2 mt-0.5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\r\n                </svg>\r\n                <span>123 Spice Street<br />Flavor City, FC 12345</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Bottom Section */}\r\n        <div className=\"border-t border-gray-800 mt-8 pt-8\">\r\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\r\n            <p className=\"text-gray-400 text-sm\">\r\n              &copy; 2024 SpiceHub. All rights reserved.\r\n            </p>\r\n            <div className=\"flex space-x-6 mt-4 md:mt-0\">\r\n              <Link href=\"/privacy\" className=\"text-gray-400 hover:text-white text-sm transition-colors\">\r\n                Privacy Policy\r\n              </Link>\r\n              <Link href=\"/terms\" className=\"text-gray-400 hover:text-white text-sm transition-colors\">\r\n                Terms of Service\r\n              </Link>\r\n              <Link href=\"/cookies\" className=\"text-gray-400 hover:text-white text-sm transition-colors\">\r\n                Cookie Policy\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEO,MAAM,SAAS;IACpB,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,MAAK;4CAAI,WAAU;sDACpB,cAAA,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;0DACnD,cAAA,6LAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAGZ,6LAAC;4CAAE,MAAK;4CAAI,WAAU;sDACpB,cAAA,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;0DACnD,cAAA,6LAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAGZ,6LAAC;4CAAE,MAAK;4CAAI,WAAU;sDACpB,cAAA,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;0DACnD,cAAA,6LAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAGZ,6LAAC;4CAAE,MAAK;4CAAI,WAAU;sDACpB,cAAA,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;0DACnD,cAAA,6LAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOhB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAqC;;;;;;;;;;;sDAC1E,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAc,WAAU;0DAAqC;;;;;;;;;;;sDAC5E,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAqC;;;;;;;;;;;sDACvE,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAgB,WAAU;0DAAqC;;;;;;;;;;;sDAC9E,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAe,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;sCAKjF,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAqC;;;;;;;;;;;sDACtE,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAqC;;;;;;;;;;;sDAC1E,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAqC;;;;;;;;;;;sDACzE,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAO,WAAU;0DAAqC;;;;;;;;;;;sDACrE,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;sCAK7E,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;oDAAe,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACtE,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;8DAEvE,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;oDAAe,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACtE,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;8DAEvE,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;oDAAsB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;;sEAC7E,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;sEACrE,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;8DAEvE,6LAAC;;wDAAK;sEAAgB,6LAAC;;;;;wDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOpC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;0CAGrC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAA2D;;;;;;kDAG3F,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAA2D;;;;;;kDAGzF,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzG;KA5Ga", "debugId": null}}, {"offset": {"line": 1908, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Ecommerce/ecommerce-app/frontend/src/app/cart/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport Link from 'next/link';\r\nimport { Header } from '@/components/layout/Header';\r\nimport { Footer } from '@/components/layout/Footer';\r\nimport { Button } from '@/components/ui/Button';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useCart } from '@/contexts/CartContext';\r\n\r\nexport default function CartPage() {\r\n  const { user } = useAuth();\r\n  const { cartItems, cartTotal, cartCount, updateQuantity, removeFromCart, isLoading } = useCart();\r\n  const router = useRouter();\r\n\r\n  useEffect(() => {\r\n    if (!user) {\r\n      router.push('/auth/login');\r\n    }\r\n  }, [user, router]);\r\n\r\n  if (!user) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">Please Login</h1>\r\n          <p className=\"text-gray-600 mb-8\">You need to be logged in to view your cart.</p>\r\n          <Link href=\"/auth/login\">\r\n            <Button>Login</Button>\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const handleQuantityChange = async (productId: string, newQuantity: number) => {\r\n    if (newQuantity < 1) {\r\n      await removeFromCart(productId);\r\n    } else {\r\n      await updateQuantity(productId, newQuantity);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50\">\r\n      <Header />\r\n      \r\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n        <div className=\"mb-8\">\r\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">Shopping Cart</h1>\r\n          <p className=\"text-gray-600\">\r\n            {cartCount > 0 ? `${cartCount} item${cartCount !== 1 ? 's' : ''} in your cart` : 'Your cart is empty'}\r\n          </p>\r\n        </div>\r\n\r\n        {cartItems.length === 0 ? (\r\n          <div className=\"text-center py-12\">\r\n            <div className=\"text-6xl mb-4\">🛒</div>\r\n            <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">Your cart is empty</h3>\r\n            <p className=\"text-gray-600 mb-8\">Start shopping to add items to your cart</p>\r\n            <Link href=\"/products\">\r\n              <Button size=\"lg\">Continue Shopping</Button>\r\n            </Link>\r\n          </div>\r\n        ) : (\r\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\r\n            {/* Cart Items */}\r\n            <div className=\"lg:col-span-2\">\r\n              <div className=\"bg-white rounded-lg shadow-md overflow-hidden\">\r\n                <div className=\"p-6 border-b border-gray-200\">\r\n                  <h2 className=\"text-lg font-semibold text-gray-900\">Cart Items</h2>\r\n                </div>\r\n                <div className=\"divide-y divide-gray-200\">\r\n                  {cartItems.map((item) => (\r\n                    <div key={item.id} className=\"p-6 flex items-center space-x-4\">\r\n                      {/* Product Image */}\r\n                      <div className=\"flex-shrink-0 w-20 h-20 bg-gray-100 rounded-lg flex items-center justify-center\">\r\n                        <div className=\"text-3xl\">{item.product?.category?.image || '🌶️'}</div>\r\n                      </div>\r\n\r\n                      {/* Product Details */}\r\n                      <div className=\"flex-1 min-w-0\">\r\n                        <Link href={`/products/${item.productId}`} className=\"hover:text-orange-600\">\r\n                          <h3 className=\"text-lg font-medium text-gray-900 truncate\">\r\n                            {item.product?.name || 'Product'}\r\n                          </h3>\r\n                        </Link>\r\n                        <p className=\"text-sm text-gray-500\">{item.product?.category?.name}</p>\r\n                        <p className=\"text-sm text-gray-500\">{item.product?.weight}</p>\r\n                        <p className=\"text-lg font-semibold text-gray-900 mt-1\">\r\n                          ${item.product?.price || 0}\r\n                        </p>\r\n                      </div>\r\n\r\n                      {/* Quantity Controls */}\r\n                      <div className=\"flex items-center space-x-3\">\r\n                        <button\r\n                          onClick={() => handleQuantityChange(item.productId, item.quantity - 1)}\r\n                          disabled={isLoading}\r\n                          className=\"w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-100 disabled:opacity-50\"\r\n                        >\r\n                          -\r\n                        </button>\r\n                        <span className=\"w-8 text-center font-medium\">{item.quantity}</span>\r\n                        <button\r\n                          onClick={() => handleQuantityChange(item.productId, item.quantity + 1)}\r\n                          disabled={isLoading || item.quantity >= (item.product?.stockQuantity || 0)}\r\n                          className=\"w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-100 disabled:opacity-50\"\r\n                        >\r\n                          +\r\n                        </button>\r\n                      </div>\r\n\r\n                      {/* Item Total */}\r\n                      <div className=\"text-right\">\r\n                        <p className=\"text-lg font-semibold text-gray-900\">\r\n                          ${item.total.toFixed(2)}\r\n                        </p>\r\n                        <button\r\n                          onClick={() => removeFromCart(item.productId)}\r\n                          disabled={isLoading}\r\n                          className=\"text-sm text-red-600 hover:text-red-700 disabled:opacity-50\"\r\n                        >\r\n                          Remove\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Order Summary */}\r\n            <div className=\"lg:col-span-1\">\r\n              <div className=\"bg-white rounded-lg shadow-md p-6 sticky top-24\">\r\n                <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Order Summary</h2>\r\n                \r\n                <div className=\"space-y-3 mb-6\">\r\n                  <div className=\"flex justify-between\">\r\n                    <span className=\"text-gray-600\">Subtotal ({cartCount} items)</span>\r\n                    <span className=\"font-medium\">${cartTotal.toFixed(2)}</span>\r\n                  </div>\r\n                  <div className=\"flex justify-between\">\r\n                    <span className=\"text-gray-600\">Shipping</span>\r\n                    <span className=\"font-medium\">\r\n                      {cartTotal >= 50 ? 'Free' : '$5.99'}\r\n                    </span>\r\n                  </div>\r\n                  <div className=\"flex justify-between\">\r\n                    <span className=\"text-gray-600\">Tax</span>\r\n                    <span className=\"font-medium\">${(cartTotal * 0.08).toFixed(2)}</span>\r\n                  </div>\r\n                  <div className=\"border-t border-gray-200 pt-3\">\r\n                    <div className=\"flex justify-between\">\r\n                      <span className=\"text-lg font-semibold\">Total</span>\r\n                      <span className=\"text-lg font-semibold\">\r\n                        ${(cartTotal + (cartTotal >= 50 ? 0 : 5.99) + cartTotal * 0.08).toFixed(2)}\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {cartTotal >= 50 ? (\r\n                  <div className=\"bg-green-50 border border-green-200 rounded-lg p-3 mb-4\">\r\n                    <p className=\"text-sm text-green-700\">\r\n                      🎉 You qualify for free shipping!\r\n                    </p>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"bg-orange-50 border border-orange-200 rounded-lg p-3 mb-4\">\r\n                    <p className=\"text-sm text-orange-700\">\r\n                      Add ${(50 - cartTotal).toFixed(2)} more for free shipping\r\n                    </p>\r\n                  </div>\r\n                )}\r\n\r\n                <Button className=\"w-full mb-3\" size=\"lg\">\r\n                  Proceed to Checkout\r\n                </Button>\r\n                \r\n                <Link href=\"/products\">\r\n                  <Button variant=\"outline\" className=\"w-full\">\r\n                    Continue Shopping\r\n                  </Button>\r\n                </Link>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </main>\r\n\r\n      <Footer />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAWe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,cAAc,EAAE,cAAc,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC7F,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,CAAC,MAAM;gBACT,OAAO,IAAI,CAAC;YACd;QACF;6BAAG;QAAC;QAAM;KAAO;IAEjB,IAAI,CAAC,MAAM;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;sCAAC;;;;;;;;;;;;;;;;;;;;;;IAKlB;IAEA,MAAM,uBAAuB,OAAO,WAAmB;QACrD,IAAI,cAAc,GAAG;YACnB,MAAM,eAAe;QACvB,OAAO;YACL,MAAM,eAAe,WAAW;QAClC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yIAAA,CAAA,SAAM;;;;;0BAEP,6LAAC;gBAAK,WAAU;;kCACd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,6LAAC;gCAAE,WAAU;0CACV,YAAY,IAAI,GAAG,UAAU,KAAK,EAAE,cAAc,IAAI,MAAM,GAAG,aAAa,CAAC,GAAG;;;;;;;;;;;;oBAIpF,UAAU,MAAM,KAAK,kBACpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAClC,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;8CAAK;;;;;;;;;;;;;;;;6CAItB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAG,WAAU;0DAAsC;;;;;;;;;;;sDAEtD,6LAAC;4CAAI,WAAU;sDACZ,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC;oDAAkB,WAAU;;sEAE3B,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;0EAAY,KAAK,OAAO,EAAE,UAAU,SAAS;;;;;;;;;;;sEAI9D,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,+JAAA,CAAA,UAAI;oEAAC,MAAM,CAAC,UAAU,EAAE,KAAK,SAAS,EAAE;oEAAE,WAAU;8EACnD,cAAA,6LAAC;wEAAG,WAAU;kFACX,KAAK,OAAO,EAAE,QAAQ;;;;;;;;;;;8EAG3B,6LAAC;oEAAE,WAAU;8EAAyB,KAAK,OAAO,EAAE,UAAU;;;;;;8EAC9D,6LAAC;oEAAE,WAAU;8EAAyB,KAAK,OAAO,EAAE;;;;;;8EACpD,6LAAC;oEAAE,WAAU;;wEAA2C;wEACpD,KAAK,OAAO,EAAE,SAAS;;;;;;;;;;;;;sEAK7B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,SAAS,IAAM,qBAAqB,KAAK,SAAS,EAAE,KAAK,QAAQ,GAAG;oEACpE,UAAU;oEACV,WAAU;8EACX;;;;;;8EAGD,6LAAC;oEAAK,WAAU;8EAA+B,KAAK,QAAQ;;;;;;8EAC5D,6LAAC;oEACC,SAAS,IAAM,qBAAqB,KAAK,SAAS,EAAE,KAAK,QAAQ,GAAG;oEACpE,UAAU,aAAa,KAAK,QAAQ,IAAI,CAAC,KAAK,OAAO,EAAE,iBAAiB,CAAC;oEACzE,WAAU;8EACX;;;;;;;;;;;;sEAMH,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;;wEAAsC;wEAC/C,KAAK,KAAK,CAAC,OAAO,CAAC;;;;;;;8EAEvB,6LAAC;oEACC,SAAS,IAAM,eAAe,KAAK,SAAS;oEAC5C,UAAU;oEACV,WAAU;8EACX;;;;;;;;;;;;;mDAhDK,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;0CA2DzB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAEzD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;;gEAAgB;gEAAW;gEAAU;;;;;;;sEACrD,6LAAC;4DAAK,WAAU;;gEAAc;gEAAE,UAAU,OAAO,CAAC;;;;;;;;;;;;;8DAEpD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEACb,aAAa,KAAK,SAAS;;;;;;;;;;;;8DAGhC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;;gEAAc;gEAAE,CAAC,YAAY,IAAI,EAAE,OAAO,CAAC;;;;;;;;;;;;;8DAE7D,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;0EACxC,6LAAC;gEAAK,WAAU;;oEAAwB;oEACpC,CAAC,YAAY,CAAC,aAAa,KAAK,IAAI,IAAI,IAAI,YAAY,IAAI,EAAE,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;wCAM/E,aAAa,mBACZ,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;0DAAyB;;;;;;;;;;iEAKxC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;;oDAA0B;oDAC/B,CAAC,KAAK,SAAS,EAAE,OAAO,CAAC;oDAAG;;;;;;;;;;;;sDAKxC,6LAAC,qIAAA,CAAA,SAAM;4CAAC,WAAU;4CAAc,MAAK;sDAAK;;;;;;sDAI1C,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,WAAU;0DAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUzD,6LAAC,yIAAA,CAAA,SAAM;;;;;;;;;;;AAGb;GAxLwB;;QACL,kIAAA,CAAA,UAAO;QAC+D,kIAAA,CAAA,UAAO;QAC/E,qIAAA,CAAA,YAAS;;;KAHF", "debugId": null}}]}