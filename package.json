{"name": "ecommerce-app", "version": "0.1.0", "private": true, "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "start": "concurrently \"npm run start:frontend\" \"npm run start:backend\"", "start:frontend": "cd frontend && npm run start", "start:backend": "cd backend && npm run start", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install"}, "devDependencies": {"concurrently": "^8.2.2"}}