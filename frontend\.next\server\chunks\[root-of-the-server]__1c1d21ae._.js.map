{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Ecommerce/ecommerce-app/frontend/src/app/api/auth/login/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\r\nimport bcrypt from 'bcryptjs';\r\nimport jwt from 'jsonwebtoken';\r\n\r\n// Mock database - In a real app, you'd use a proper database\r\nconst users = [\r\n  {\r\n    id: '1',\r\n    name: 'Admin User',\r\n    email: '<EMAIL>',\r\n    password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password: admin123\r\n    role: 'admin' as const,\r\n    phone: '+1234567890',\r\n    createdAt: new Date('2024-01-01'),\r\n    updatedAt: new Date('2024-01-01'),\r\n  },\r\n  {\r\n    id: '2',\r\n    name: 'Customer User',\r\n    email: '<EMAIL>',\r\n    password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password: user123\r\n    role: 'user' as const,\r\n    phone: '+1234567891',\r\n    createdAt: new Date('2024-01-01'),\r\n    updatedAt: new Date('2024-01-01'),\r\n  },\r\n];\r\n\r\nconst JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';\r\n\r\nexport async function POST(request: NextRequest) {\r\n  try {\r\n    const { email, password, role } = await request.json();\r\n\r\n    // Validate input\r\n    if (!email || !password || !role) {\r\n      return NextResponse.json(\r\n        { success: false, message: 'Email, password, and role are required' },\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    // Find user by email and role\r\n    const user = users.find(u => u.email === email && u.role === role);\r\n    \r\n    if (!user) {\r\n      return NextResponse.json(\r\n        { success: false, message: 'Invalid credentials or role' },\r\n        { status: 401 }\r\n      );\r\n    }\r\n\r\n    // For demo purposes, we'll accept both the hashed password and plain text\r\n    const isValidPassword = \r\n      await bcrypt.compare(password, user.password) ||\r\n      (role === 'admin' && password === 'admin123') ||\r\n      (role === 'user' && password === 'user123');\r\n\r\n    if (!isValidPassword) {\r\n      return NextResponse.json(\r\n        { success: false, message: 'Invalid credentials' },\r\n        { status: 401 }\r\n      );\r\n    }\r\n\r\n    // Generate JWT token\r\n    const token = jwt.sign(\r\n      { \r\n        userId: user.id, \r\n        email: user.email, \r\n        role: user.role \r\n      },\r\n      JWT_SECRET,\r\n      { expiresIn: '7d' }\r\n    );\r\n\r\n    // Return user data (excluding password)\r\n    const { password: _, ...userWithoutPassword } = user;\r\n\r\n    return NextResponse.json({\r\n      success: true,\r\n      message: 'Login successful',\r\n      token,\r\n      user: userWithoutPassword,\r\n    });\r\n\r\n  } catch (error) {\r\n    console.error('Login error:', error);\r\n    return NextResponse.json(\r\n      { success: false, message: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,6DAA6D;AAC7D,MAAM,QAAQ;IACZ;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,MAAM;QACN,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,MAAM;QACN,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAED,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAEtC,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEpD,iBAAiB;QACjB,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,MAAM;YAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAyC,GACpE;gBAAE,QAAQ;YAAI;QAElB;QAEA,8BAA8B;QAC9B,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,SAAS,EAAE,IAAI,KAAK;QAE7D,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAA8B,GACzD;gBAAE,QAAQ;YAAI;QAElB;QAEA,0EAA0E;QAC1E,MAAM,kBACJ,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU,KAAK,QAAQ,KAC3C,SAAS,WAAW,aAAa,cACjC,SAAS,UAAU,aAAa;QAEnC,IAAI,CAAC,iBAAiB;YACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAsB,GACjD;gBAAE,QAAQ;YAAI;QAElB;QAEA,qBAAqB;QACrB,MAAM,QAAQ,uIAAA,CAAA,UAAG,CAAC,IAAI,CACpB;YACE,QAAQ,KAAK,EAAE;YACf,OAAO,KAAK,KAAK;YACjB,MAAM,KAAK,IAAI;QACjB,GACA,YACA;YAAE,WAAW;QAAK;QAGpB,wCAAwC;QACxC,MAAM,EAAE,UAAU,CAAC,EAAE,GAAG,qBAAqB,GAAG;QAEhD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT;YACA,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,SAAS;QAAwB,GACnD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}